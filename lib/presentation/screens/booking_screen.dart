import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../data/models/villa_model.dart';
import '../../data/providers/booking_provider.dart';
import '../../data/providers/villa_provider.dart';

class BookingScreen extends StatefulWidget {
  final String villaId;
  final DateTime? fromDate; // New argument
  final DateTime? toDate; // New argument

  const BookingScreen({
    super.key,
    required this.villaId,
    this.fromDate,
    this.toDate,
  });

  @override
  State<BookingScreen> createState() => _BookingScreenState();
}

class _BookingScreenState extends State<BookingScreen> {
  int _guestCount = 2; // Default or load from bookingProvider if already set

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      final bookingProvider = Provider.of<BookingProvider>(
        context,
        listen: false,
      );
      final villa = villaProvider.getVillaById(widget.villaId);

      if (villa != null) {
        // Initialize booking first (sets default nights, etc.)
        bookingProvider.initBooking(villa);

        // If dates are passed from CalendarScreen, update the booking
        if (widget.fromDate != null && widget.toDate != null) {
          bookingProvider.setSelectedDateRange(
            DateTimeRange(start: widget.fromDate!, end: widget.toDate!),
          );
        }
        // Update guest count from booking model if it exists
        if (bookingProvider.currentBooking?.numberOfGuests != null &&
            bookingProvider.currentBooking!.numberOfGuests! > 0) {
          _guestCount = bookingProvider.currentBooking!.numberOfGuests!;
        } else {
          // Set default guest count in provider if not already set by initBooking or above
          bookingProvider.updateNumberOfGuests(_guestCount);
        }
      } else {
        // Handle villa not found case, maybe navigate back or show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Error: Villa details not found.')),
          );
          Navigator.of(context).pop();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final villaProvider = Provider.of<VillaProvider>(context);
    final bookingProvider = Provider.of<BookingProvider>(context);
    final villa = villaProvider.getVillaById(widget.villaId);
    final booking = bookingProvider.currentBooking;
    final theme = Theme.of(context);

    if (villa == null || booking == null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Villa image
                  _buildVillaImage(villa),

                  // Villa details
                  _buildVillaDetails(villa),

                  // Divider
                  Divider(
                    height: 32,
                    thickness: 1,
                    color: theme.dividerColor.withOpacity(0.3),
                  ),

                  // Date selection
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Selected Dates',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildDateDisplay(context, booking),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Guest selection
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Guest Details',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildGuestDetailsCard(
                          context,
                          booking,
                          bookingProvider,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Price details
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Price Details',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPriceDetails(booking),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Book Now button
          _buildBookNowButton(context),
        ],
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    return AppBar(
      elevation: 0,
      backgroundColor: theme.scaffoldBackgroundColor,
      foregroundColor: theme.colorScheme.onSurface,
      centerTitle: true,
      title: Text(
        'Book Your Stay',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, size: 20),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildVillaImage(Villa villa) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.network(
          villa.imageUrl,
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: 200,
              width: double.infinity,
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.villa,
                    size: 60,
                    color: Theme.of(
                      context,
                    ).iconTheme.color?.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Villa Image',
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              height: 200,
              width: double.infinity,
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              child: Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildVillaDetails(Villa villa) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Villa name and price row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  villa.name,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '₹${villa.price.toInt()}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  Text(
                    'per night',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Location and rating row
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  villa.location,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.8),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.star_rate_rounded, size: 16, color: Colors.amber),
              const SizedBox(width: 4),
              Text(
                villa.rating.toStringAsFixed(1),
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Amenities section
          if (villa.amenities != null && villa.amenities!.isNotEmpty) ...[
            Text(
              'Amenities',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 6,
              children:
                  villa.amenities!.take(6).map((amenity) {
                    IconData icon;
                    switch (amenity.toLowerCase()) {
                      case 'pool':
                      case 'swimming pool':
                        icon = Icons.pool;
                        break;
                      case 'wifi':
                      case 'wi-fi':
                        icon = Icons.wifi;
                        break;
                      case 'ac':
                      case 'air conditioning':
                        icon = Icons.ac_unit;
                        break;
                      case 'parking':
                        icon = Icons.local_parking;
                        break;
                      case 'kitchen':
                        icon = Icons.kitchen;
                        break;
                      case 'gym':
                      case 'fitness':
                        icon = Icons.fitness_center;
                        break;
                      case 'tv':
                      case 'television':
                        icon = Icons.tv;
                        break;
                      case 'balcony':
                        icon = Icons.balcony;
                        break;
                      default:
                        icon = Icons.check_circle_outline;
                    }
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colorScheme.primary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            icon,
                            size: 14,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            amenity,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
            if (villa.amenities!.length > 6) ...[
              const SizedBox(height: 6),
              Text(
                '+${villa.amenities!.length - 6} more amenities',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
            const SizedBox(height: 16),
          ],

          // Description if available
          if (villa.description != null && villa.description!.isNotEmpty) ...[
            Text(
              'About this villa',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              villa.description!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.8),
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGuestDetailsCard(
    BuildContext context,
    dynamic booking,
    BookingProvider bookingProvider,
  ) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Compact guest count in a single row
            Row(
              children: [
                Expanded(
                  child: _buildCompactGuestCount(
                    'Adults',
                    booking.noOfAdult ?? 1,
                    () {
                      if ((booking.noOfAdult ?? 1) > 1) {
                        bookingProvider.updateCurrentBooking(
                          booking.copyWith(
                            noOfAdult: (booking.noOfAdult ?? 1) - 1,
                          ),
                        );
                      }
                    },
                    () {
                      bookingProvider.updateCurrentBooking(
                        booking.copyWith(
                          noOfAdult: (booking.noOfAdult ?? 1) + 1,
                        ),
                      );
                    },
                    theme,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCompactGuestCount(
                    'Children',
                    booking.numberOfChildren ?? 0,
                    () {
                      if ((booking.numberOfChildren ?? 0) > 0) {
                        bookingProvider.updateCurrentBooking(
                          booking.copyWith(
                            numberOfChildren:
                                (booking.numberOfChildren ?? 0) - 1,
                          ),
                        );
                      }
                    },
                    () {
                      bookingProvider.updateCurrentBooking(
                        booking.copyWith(
                          numberOfChildren: (booking.numberOfChildren ?? 0) + 1,
                        ),
                      );
                    },
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactGuestCount(
    String label,
    int count,
    VoidCallback onDecrement,
    VoidCallback onIncrement,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: theme.colorScheme.primary),
                ),
                child: IconButton(
                  onPressed: onDecrement,
                  icon: const Icon(Icons.remove, size: 16),
                  color: theme.colorScheme.primary,
                  padding: EdgeInsets.zero,
                ),
              ),
              Text(
                count.toString(),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: theme.colorScheme.primary,
                ),
                child: IconButton(
                  onPressed: onIncrement,
                  icon: const Icon(Icons.add, size: 16),
                  color: Colors.white,
                  padding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGuestCountRow(
    BuildContext context,
    String label,
    int count,
    VoidCallback onDecrement,
    VoidCallback onIncrement,
    Color accentColor,
  ) {
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: accentColor),
              ),
              child: IconButton(
                onPressed: onDecrement,
                icon: const Icon(Icons.remove, size: 18),
                color: accentColor,
                constraints: const BoxConstraints(minHeight: 36, minWidth: 36),
                padding: EdgeInsets.zero,
              ),
            ),
            Container(
              width: 60,
              alignment: Alignment.center,
              child: Text(
                count.toString(),
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: accentColor,
              ),
              child: IconButton(
                onPressed: onIncrement,
                icon: const Icon(Icons.add, size: 18),
                color: Colors.white,
                constraints: const BoxConstraints(minHeight: 36, minWidth: 36),
                padding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Renamed from _buildDateSelection to _buildDateDisplay
  // Dates are now read-only on this screen
  Widget _buildDateDisplay(BuildContext context, dynamic booking) {
    final dateFormat = DateFormat('MMM d, yyyy');

    return Row(
      children: [
        _buildDateBox(
          context: context,
          label: 'Check-in',
          value:
              booking.checkInDate != null
                  ? dateFormat.format(booking.checkInDate!)
                  : 'Not set',
          // onTap: () {} // Disabled: Dates are set from CalendarScreen
        ),
        const SizedBox(width: 16),
        _buildDateBox(
          context: context,
          label: 'Check-out',
          value:
              booking.checkOutDate != null
                  ? dateFormat.format(booking.checkOutDate!)
                  : 'Not set',
          // onTap: () {} // Disabled: Dates are set from CalendarScreen
        ),
      ],
    );
  }

  Widget _buildDateBox({
    required BuildContext context,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          border: Border.all(color: theme.colorScheme.outline.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuestSelection(BuildContext context, dynamic booking) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Minus button
          IconButton(
            onPressed: () {
              if (_guestCount > 1) {
                setState(() => _guestCount--);
                Provider.of<BookingProvider>(
                  context,
                  listen: false,
                ).updateNumberOfGuests(_guestCount);
              }
            },
            icon: const Icon(Icons.remove),
            color: Theme.of(context).primaryColor,
          ),

          // Guest count
          Text(
            '$_guestCount guests',
            style: textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
          ),

          // Plus button
          IconButton(
            onPressed: () {
              if (_guestCount < 10) {
                setState(() => _guestCount++);
                Provider.of<BookingProvider>(
                  context,
                  listen: false,
                ).updateNumberOfGuests(_guestCount);
              }
            },
            icon: const Icon(Icons.add),
            color: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceDetails(dynamic booking) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _priceRow(
              '₹${booking.pricePerNight.toInt()} × ${booking.numberOfNights} nights',
              '₹${(booking.pricePerNight * booking.numberOfNights).toInt()}',
              theme,
            ),
            const SizedBox(height: 12),
            _priceRow('Cleaning fee', '₹${booking.cleaningFee.toInt()}', theme),
            const SizedBox(height: 12),
            _priceRow('Service fee', '₹${booking.serviceFee.toInt()}', theme),
            const SizedBox(height: 16),
            Divider(color: theme.dividerColor.withOpacity(0.3)),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  '₹${booking.totalPrice.toInt()}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _priceRow(String title, String amount, ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        Text(
          amount,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildBookNowButton(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          _handleBooking(context);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30), // Matches project theme
          ),
          elevation: 0, // Matches project theme
          textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        child: const Text('Book Now'),
      ),
    );
  }

  // _selectDate method is removed as date selection is handled by CalendarScreen.
  // If a "Change Dates" button is desired, it should navigate back to CalendarScreen.

  void _handleBooking(BuildContext context) {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    // Process booking
    final bookingProvider = Provider.of<BookingProvider>(
      context,
      listen: false,
    );
    bookingProvider.confirmBooking().then((success) {
      // Changed to confirmBooking
      // Close loading dialog
      Navigator.pop(context);

      if (success) {
        // Show success dialog
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Booking Confirmed'),
                content: const Text(
                  'Your booking has been confirmed successfully!',
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context); // Close dialog
                      Navigator.pop(context); // Go back to previous screen
                    },
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      } else {
        // Show error dialog
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Booking Failed'),
                content: const Text(
                  'There was an error processing your booking. Please try again.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      }
    });
  }
}

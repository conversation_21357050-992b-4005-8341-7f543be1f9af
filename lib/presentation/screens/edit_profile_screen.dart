import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../data/providers/user_provider.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _contactNumberController;
  late TextEditingController _emailController;
  late TextEditingController _genderController;
  late TextEditingController _dobController;
  late TextEditingController _profilePictureController;

  @override
  void initState() {
    super.initState();
    final user = Provider.of<UserProvider>(context, listen: false).currentUser;
    _nameController = TextEditingController(text: user?.name ?? '');
    _contactNumberController = TextEditingController(text: user?.contactNumber ?? '');
    _emailController = TextEditingController(text: user?.email ?? '');
    _genderController = TextEditingController(text: user?.gender ?? '');
    _dobController = TextEditingController(text: user?.dob ?? '');
    _profilePictureController = TextEditingController(text: user?.profilePicture ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _contactNumberController.dispose();
    _emailController.dispose();
    _genderController.dispose();
    _dobController.dispose();
    _profilePictureController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final user = userProvider.currentUser;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        title: Text(
          'Edit Profile',
          style: Theme.of(context).appBarTheme.titleTextStyle,
        ),
        centerTitle: true,
      ),
      body: user == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 24),
                      Center(
                        child: CircleAvatar(
                          radius: 48,
                          backgroundImage: user.profilePicture != null && user.profilePicture!.isNotEmpty
                              ? NetworkImage(user.profilePicture!)
                              : null,
                          child: (user.profilePicture == null || user.profilePicture!.isEmpty)
                              ? const Icon(Icons.person, size: 48)
                              : null,
                        ),
                      ),
                      const SizedBox(height: 24),
                      _buildProfileField(
                        controller: _nameController,
                        label: 'Name',
                        validatorMsg: 'Name cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 16),
                      _buildProfileField(
                        controller: _contactNumberController,
                        label: 'Contact Number',
                        validatorMsg: 'Contact Number cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 16),
                      _buildProfileField(
                        controller: _genderController,
                        label: 'Gender',
                        validatorMsg: 'Gender cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 16),
                      _buildProfileField(
                        controller: _dobController,
                        label: 'Date of Birth',
                        validatorMsg: 'Date of Birth cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 16),
                      _buildProfileField(
                        controller: _profilePictureController,
                        label: 'Profile Picture URL',
                        validatorMsg: 'Profile Picture URL cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 16),
                      _buildProfileField(
                        controller: _emailController,
                        label: 'Email',
                        validatorMsg: 'Email cannot be empty',
                        context: context,
                      ),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[200],
                            foregroundColor: Colors.black,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                            minimumSize: const Size(150, 48),
                          ),
                          onPressed: () async {
                            if (_formKey.currentState?.validate() ?? false) {
                              await userProvider.updateUserProfile(
                                name: _nameController.text,
                                contactNumber: _contactNumberController.text,
                                email: _emailController.text,
                                gender: _genderController.text,
                                dob: _dobController.text,
                                profilePicture: _profilePictureController.text,
                              );
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('Profile updated')),
                                );
                                Navigator.of(context).pop();
                              }
                            }
                          },
                          child: const Text('Save Changes'),
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildProfileField({
    required TextEditingController controller,
    required String label,
    required String validatorMsg,
    required BuildContext context,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).inputDecorationTheme.enabledBorder?.borderSide.color ??
                Colors.grey,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
      ),
      style: TextStyle(
        color: Theme.of(context).textTheme.bodyLarge?.color,
        fontSize: 16,
      ),
      validator: (value) =>
          value == null || value.isEmpty ? validatorMsg : null,
    );
  }
}

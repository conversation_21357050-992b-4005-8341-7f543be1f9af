import 'package:flutter/material.dart';
import '../../routes.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  final TextEditingController _searchController = TextEditingController();

  // Mock data for chat conversations
  final List<ChatConversation> _conversations = [
    ChatConversation(
      id: '1',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      lastMessage: 'Online • Available for chat',
      timestamp: DateTime.now(),
      isOnline: true,
      badge: 'Villa Owner',
      unreadCount: 0,
    ),
    ChatConversation(
      id: '2',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      lastMessage: 'Hey, I have a question about...',
      timestamp: DateTime.now().subtract(const Duration(minutes: 2)),
      isOnline: true,
      unreadCount: 3,
    ),
    ChatConversation(
      id: '3',
      name: 'Team Alpha',
      avatar: 'https://randomuser.me/api/portraits/men/86.jpg',
      lastMessage: 'Meeting scheduled for tomorrow...',
      timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
      isOnline: false,
      unreadCount: 0,
    ),
    ChatConversation(
      id: '4',
      name: 'John Miller',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
      lastMessage: 'Thanks for the update!',
      timestamp: DateTime.now().subtract(const Duration(hours: 1)),
      isOnline: true,
      unreadCount: 0,
    ),
    ChatConversation(
      id: '5',
      name: 'Support Group',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      lastMessage: 'New member joined the villa...',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isOnline: false,
      unreadCount: 0,
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header with title and new message button
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // User avatar
                      const CircleAvatar(
                        radius: 16,
                        backgroundImage: NetworkImage(
                          'https://randomuser.me/api/portraits/men/32.jpg',
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Title
                      Text(
                        'Messages',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color:
                              Theme.of(context).textTheme.headlineMedium?.color,
                        ),
                      ),
                    ],
                  ),
                  // New message button
                  IconButton(
                    icon: Icon(
                      Icons.add,
                      size: 24,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    onPressed: () {
                      // TODO: Implement new message functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('New message feature coming soon'),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // Search bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: TextField(
                  controller: _searchController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Search conversations',
                    hintStyle: TextStyle(
                      color: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                    ),
                    border: InputBorder.none,
                    icon: Icon(
                      Icons.search,
                      color: Theme.of(
                        context,
                      ).iconTheme.color?.withValues(alpha: 0.7),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),

            // Chat list
            Expanded(
              child: ListView.builder(
                itemCount: _conversations.length,
                itemBuilder: (context, index) {
                  return _buildChatItem(_conversations[index]);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatItem(ChatConversation conversation) {
    // Format timestamp
    String timeText = _formatTimestamp(conversation.timestamp);

    return InkWell(
      onTap: () {
        // Navigate to chat detail screen
        Navigator.of(context).push(
          AppRoutes.generateChatDetailRoute(
            context,
            conversationId: conversation.id,
            hostName: conversation.name,
            hostAvatar: conversation.avatar,
            villaName: 'Villa Serenity', // Using a fixed villa name for demo
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Avatar with online indicator
            Stack(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundImage: NetworkImage(conversation.avatar),
                ),
                if (conversation.isOnline)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context).scaffoldBackgroundColor,
                          width: 2,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 12),

            // Message content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and badge
                  Row(
                    children: [
                      Text(
                        conversation.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                      if (conversation.badge != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            conversation.badge!,
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  Theme.of(
                                    context,
                                  ).colorScheme.onPrimaryContainer,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Last message
                  Text(
                    conversation.lastMessage,
                    style: TextStyle(
                      color:
                          conversation.unreadCount > 0
                              ? Theme.of(context).textTheme.bodyMedium?.color
                              : Theme.of(context).textTheme.bodyMedium?.color
                                  ?.withValues(alpha: 0.7),
                      fontWeight:
                          conversation.unreadCount > 0
                              ? FontWeight.w500
                              : FontWeight.normal,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Time and unread count
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  timeText,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(
                      context,
                    ).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 4),
                if (conversation.unreadCount > 0)
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      conversation.unreadCount.toString(),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} mins ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    }
  }
}

class ChatConversation {
  final String id;
  final String name;
  final String avatar;
  final String lastMessage;
  final DateTime timestamp;
  final bool isOnline;
  final String? badge;
  final int unreadCount;

  ChatConversation({
    required this.id,
    required this.name,
    required this.avatar,
    required this.lastMessage,
    required this.timestamp,
    required this.isOnline,
    this.badge,
    required this.unreadCount,
  });
}

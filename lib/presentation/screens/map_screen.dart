import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

import '../../data/models/api_villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../routes.dart';
import '../../core/utils/location_service.dart';
import '../../core/utils/service_locator.dart';
import '../widgets/filter_bottom_sheet.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final TextEditingController _searchController = TextEditingController();
  bool _hasMapError = false;
  bool _isLoadingLocation = true;
  bool _isSearchActive = false;

  // Initial camera position - will be updated with user's location
  CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(
      19.281112,
      73.047047,
    ), // Default to Mumbai (matches LocationService)
    zoom: 12,
  );

  @override
  void initState() {
    super.initState();
    _initializeLocation();

    // Defer villa fetching until after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchVillas();
    });
  }

  Future<void> _initializeLocation() async {
    try {
      // Get user's current location
      Position position = await LocationService.getCachedOrCurrentLocation();

      // Update initial camera position with user's location
      setState(() {
        _initialCameraPosition = CameraPosition(
          target: LatLng(position.latitude, position.longitude),
          zoom: 12,
        );
        _isLoadingLocation = false;
      });

      // Move camera to user's location if map is already created
      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(LatLng(position.latitude, position.longitude)),
        );
      }

      // Villa markers will be added after API call in _fetchVillas
    } catch (e) {
      // If location fails, use default position
      setState(() {
        _isLoadingLocation = false;
      });
      // Villa markers will be added after API call in _fetchVillas
    }
  }

  Future<void> _fetchVillas() async {
    try {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      await villaProvider.fetchAllVillas();

      // Add villa markers after data is loaded
      if (mounted) {
        _addVillaMarkers();
      }
    } catch (e) {
      // Handle error silently or show error message
      sl.logger.e('Error fetching villas: $e');
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Create custom marker icon with villa details
  Future<BitmapDescriptor> _createCustomMarker(ApiVilla villa) async {
    const double markerWidth = 420;
    const double markerHeight = 210;

    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    // Draw drop shadow for depth
    final Paint shadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.22)
          ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 12);
    final RRect shadowRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(8, 12, markerWidth - 16, markerHeight - 38),
      const Radius.circular(26),
    );
    canvas.drawRRect(shadowRect, shadowPaint);

    // Gradient background for main container
    final Rect gradientRect = Rect.fromLTWH(
      0,
      0,
      markerWidth,
      markerHeight - 30,
    );
    final Paint gradientPaint =
        Paint()
          ..shader = ui.Gradient.linear(
            Offset(0, 0),
            Offset(markerWidth, markerHeight - 30),
            [Colors.blue.shade600, Colors.lightBlueAccent.shade100],
          )
          ..style = PaintingStyle.fill;

    // Glass highlight effect
    final Paint glassPaint =
        Paint()
          ..shader = ui.Gradient.linear(Offset(0, 0), Offset(markerWidth, 0), [
            Colors.white.withValues(alpha: 0.18),
            Colors.transparent,
          ])
          ..style = PaintingStyle.fill;

    // Main container with gradient
    final RRect containerRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, markerWidth, markerHeight - 30),
      const Radius.circular(22),
    );
    canvas.drawRRect(containerRect, gradientPaint);

    // Glass highlight
    final RRect glassRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, markerWidth, (markerHeight - 30) * 0.45),
      const Radius.circular(22),
    );
    canvas.drawRRect(glassRect, glassPaint);

    // Colored border
    final Paint borderPaint =
        Paint()
          ..color = Colors.blueAccent
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3.5;
    canvas.drawRRect(containerRect, borderPaint);

    // Draw pointer triangle with shadow
    final Path pointerShadow = Path();
    pointerShadow.moveTo(markerWidth / 2 - 22, markerHeight - 32);
    pointerShadow.lineTo(markerWidth / 2, markerHeight - 10);
    pointerShadow.lineTo(markerWidth / 2 + 22, markerHeight - 32);
    pointerShadow.close();
    final Paint pointerShadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.18)
          ..maskFilter = const ui.MaskFilter.blur(ui.BlurStyle.normal, 4);
    canvas.drawPath(pointerShadow, pointerShadowPaint);

    // Pointer triangle (main)
    final Paint pointerPaint =
        Paint()
          ..shader = ui.Gradient.linear(
            Offset(markerWidth / 2 - 18, markerHeight - 30),
            Offset(markerWidth / 2 + 18, markerHeight - 30),
            [Colors.blue.shade600, Colors.lightBlueAccent.shade100],
          )
          ..style = PaintingStyle.fill;
    final Path pointer = Path();
    pointer.moveTo(markerWidth / 2 - 18, markerHeight - 30);
    pointer.lineTo(markerWidth / 2, markerHeight);
    pointer.lineTo(markerWidth / 2 + 18, markerHeight - 30);
    pointer.close();
    canvas.drawPath(pointer, pointerPaint);

    // Pointer border
    final Paint pointerBorderPaint =
        Paint()
          ..color = Colors.blueAccent
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.2;
    canvas.drawPath(pointer, pointerBorderPaint);

    // Text styles (modern, bold)
    final TextStyle priceStyle = TextStyle(
      color: Colors.white,
      fontSize: 36,
      fontWeight: FontWeight.w900,
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.18),
          blurRadius: 4,
          offset: const Offset(1, 2),
        ),
      ],
    );

    final TextStyle ratingStyle = TextStyle(
      color: Colors.yellow.shade400,
      fontSize: 28,
      fontWeight: FontWeight.w700,
      letterSpacing: 0.2,
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.12),
          blurRadius: 2,
          offset: const Offset(1, 1),
        ),
      ],
    );

    final TextStyle nameStyle = TextStyle(
      color: Colors.white,
      fontSize: 25,
      fontWeight: FontWeight.w700,
      letterSpacing: 0.1,
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.13),
          blurRadius: 2,
          offset: const Offset(1, 1),
        ),
      ],
    );

    // Draw price (with pill background)
    final String priceText = '₹${villa.effectiveWeekdayPrice.toInt()}';
    final TextPainter pricePainter = TextPainter(
      text: TextSpan(text: priceText, style: priceStyle),
      textDirection: TextDirection.ltr,
    );
    pricePainter.layout();
    final double priceBgWidth = pricePainter.width + 32;
    final double priceBgHeight = pricePainter.height + 10;
    final RRect priceBgRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(12, 14, priceBgWidth, priceBgHeight),
      const Radius.circular(18),
    );
    final Paint priceBgPaint =
        Paint()
          ..shader = ui.Gradient.linear(
            Offset(12, 14),
            Offset(12 + priceBgWidth, 14 + priceBgHeight),
            [Colors.green.shade400, Colors.greenAccent.shade200],
          );
    canvas.drawRRect(priceBgRect, priceBgPaint);
    pricePainter.paint(canvas, Offset(28, 19));

    // Draw rating with star (with pill background)
    final String ratingText = '★ ${villa.rating}';
    final TextPainter ratingPainter = TextPainter(
      text: TextSpan(text: ratingText, style: ratingStyle),
      textDirection: TextDirection.ltr,
    );
    ratingPainter.layout();
    final double ratingBgWidth = ratingPainter.width + 28;
    final double ratingBgHeight = ratingPainter.height + 8;
    final RRect ratingBgRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        markerWidth - ratingBgWidth - 14,
        16,
        ratingBgWidth,
        ratingBgHeight,
      ),
      const Radius.circular(16),
    );
    final Paint ratingBgPaint =
        Paint()..color = Colors.black.withValues(alpha: 0.18);
    canvas.drawRRect(ratingBgRect, ratingBgPaint);
    ratingPainter.paint(
      canvas,
      Offset(markerWidth - ratingPainter.width - 28, 20),
    );

    // Draw villa name (truncated, centered)
    String nameText = villa.name;
    if (nameText.length > 26) {
      nameText = '${nameText.substring(0, 23)}...';
    }
    final TextPainter namePainter = TextPainter(
      text: TextSpan(text: nameText, style: nameStyle),
      textDirection: TextDirection.ltr,
      maxLines: 1,
      ellipsis: '...',
    );
    namePainter.layout(maxWidth: markerWidth - 36);
    namePainter.paint(canvas, Offset(18, 80));

    // Draw amenity icons (with subtle glass background)
    const double iconSize = 30;
    const double iconSpacing = 42;
    double iconX = 18;
    const double iconY = 142;

    final Paint iconBgPaint =
        Paint()..color = Colors.white.withValues(alpha: 0.18);

    // Room icon
    if (villa.noOfRoom > 0) {
      final RRect iconBg = RRect.fromRectAndRadius(
        Rect.fromLTWH(iconX - 4, iconY - 4, iconSize + 28, iconSize + 8),
        const Radius.circular(12),
      );
      canvas.drawRRect(iconBg, iconBgPaint);
      _drawIcon(canvas, Icons.bed, iconX, iconY, iconSize, Colors.white);
      _drawText(
        canvas,
        '${villa.noOfRoom}',
        iconX + iconSize + 6,
        iconY + 6,
        TextStyle(
          fontSize: 21,
          color: Colors.white,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.13),
              blurRadius: 2,
              offset: const Offset(1, 1),
            ),
          ],
        ),
      );
      iconX += iconSpacing + 18;
    }

    // WiFi icon
    if (villa.wifi) {
      final RRect iconBg = RRect.fromRectAndRadius(
        Rect.fromLTWH(iconX - 4, iconY - 4, iconSize + 8, iconSize + 8),
        const Radius.circular(12),
      );
      canvas.drawRRect(iconBg, iconBgPaint);
      _drawIcon(canvas, Icons.wifi, iconX, iconY, iconSize, Colors.white);
      iconX += iconSpacing;
    }

    // Pool icon
    if (villa.isSwimmingPool) {
      final RRect iconBg = RRect.fromRectAndRadius(
        Rect.fromLTWH(iconX - 4, iconY - 4, iconSize + 8, iconSize + 8),
        const Radius.circular(12),
      );
      canvas.drawRRect(iconBg, iconBgPaint);
      _drawIcon(canvas, Icons.pool, iconX, iconY, iconSize, Colors.white);
      iconX += iconSpacing;
    }

    // AC icon
    if (villa.ac) {
      final RRect iconBg = RRect.fromRectAndRadius(
        Rect.fromLTWH(iconX - 4, iconY - 4, iconSize + 8, iconSize + 8),
        const Radius.circular(12),
      );
      canvas.drawRRect(iconBg, iconBgPaint);
      _drawIcon(canvas, Icons.ac_unit, iconX, iconY, iconSize, Colors.white);
    }

    final ui.Picture picture = pictureRecorder.endRecording();
    final ui.Image image = await picture.toImage(
      markerWidth.toInt(),
      markerHeight.toInt(),
    );
    final ByteData? byteData = await image.toByteData(
      format: ui.ImageByteFormat.png,
    );

    return BitmapDescriptor.fromBytes(byteData!.buffer.asUint8List());
  }

  // Helper method to draw icons on canvas
  void _drawIcon(
    Canvas canvas,
    IconData iconData,
    double x,
    double y,
    double size,
    Color color,
  ) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(iconData.codePoint),
        style: TextStyle(
          fontSize: size,
          fontFamily: iconData.fontFamily,
          color: color,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x, y));
  }

  // Helper method to draw text on canvas
  void _drawText(
    Canvas canvas,
    String text,
    double x,
    double y,
    TextStyle style,
  ) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x, y));
  }

  Future<void> _addVillaMarkers() async {
    final villaProvider = Provider.of<VillaProvider>(context, listen: false);

    // Use map search results if available, otherwise use all villas
    final apiVillas =
        _isSearchActive && villaProvider.mapSearchResults.isNotEmpty
            ? villaProvider.mapSearchResults
            : villaProvider.apiVillas;

    final newMarkers = <Marker>{};

    for (var villa in apiVillas) {
      // Check if villa has valid coordinates
      final latitude = villa.latitude;
      final longitude = villa.longitude;

      if (latitude != null && longitude != null) {
        // Create custom marker icon
        final BitmapDescriptor customIcon = await _createCustomMarker(villa);

        newMarkers.add(
          Marker(
            markerId: MarkerId(villa.id.toString()),
            position: LatLng(latitude, longitude),
            icon: customIcon,
            infoWindow: InfoWindow(
              title: villa.name,
              snippet:
                  '₹${villa.effectiveWeekdayPrice.toInt()}/night • ${villa.rating}★',
              onTap: () async {
                await _navigateToVillaDetail(villa.id.toString());
              },
            ),
            onTap: () {
              _showVillaBottomSheet(villa);
            },
            anchor: const Offset(
              0.5,
              1.0,
            ), // Position marker correctly with custom icon
          ),
        );
      }
    }

    if (mounted) {
      setState(() {
        _markers.clear();
        _markers.addAll(newMarkers);
      });

      // If no markers are visible in the current view, zoom out to show all
      if (newMarkers.isNotEmpty && _mapController != null) {
        LatLngBounds bounds = _boundsFromMarkers(newMarkers);
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 100.0), // Add padding
        );
      }
    }
  }

  // Helper to calculate LatLngBounds from a set of markers
  LatLngBounds _boundsFromMarkers(Set<Marker> markers) {
    double? minLat, maxLat, minLng, maxLng;

    for (var marker in markers) {
      if (minLat == null || marker.position.latitude < minLat) {
        minLat = marker.position.latitude;
      }
      if (maxLat == null || marker.position.latitude > maxLat) {
        maxLat = marker.position.latitude;
      }
      if (minLng == null || marker.position.longitude < minLng) {
        minLng = marker.position.longitude;
      }
      if (maxLng == null || marker.position.longitude > maxLng) {
        maxLng = marker.position.longitude;
      }
    }

    return LatLngBounds(
      southwest: LatLng(minLat!, minLng!),
      northeast: LatLng(maxLat!, maxLng!),
    );
  }

  Future<void> _performSearch(String searchTerm) async {
    if (searchTerm.trim().isEmpty) {
      // If search is empty, clear search results and reset to show all villas
      setState(() {
        _isSearchActive = false;
      });
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      villaProvider
          .clearMapSearchResults(); // Clear the separate search results
      await _addVillaMarkers(); // Refresh markers with all villas
      return;
    }

    setState(() {
      _isSearchActive = true;
    });

    try {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);

      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Searching villas...'),
          duration: Duration(seconds: 1),
        ),
      );

      // Filter villas locally based on search term from existing data
      final allVillas = villaProvider.apiVillas;
      final filteredVillas =
          allVillas.where((villa) {
            final searchLower = searchTerm.toLowerCase();
            return villa.name.toLowerCase().contains(searchLower) ||
                villa.address.toLowerCase().contains(searchLower) ||
                villa.area.toLowerCase().contains(searchLower) ||
                villa.villaGroupName.toLowerCase().contains(searchLower);
          }).toList();

      // Store filtered results in the separate map search results
      // Use the proper method to set search results
      villaProvider.setMapSearchResults(filteredVillas);

      // Refresh markers with search results
      await _addVillaMarkers();

      // Move camera to first result if available
      if (filteredVillas.isNotEmpty && _mapController != null) {
        final firstVilla = filteredVillas.first;
        final lat = firstVilla.latitude;
        final lng = firstVilla.longitude;
        if (lat != null && lng != null) {
          _mapController!.animateCamera(
            CameraUpdate.newLatLngZoom(LatLng(lat, lng), 14),
          );
        }
      }

      // Show results message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Found ${filteredVillas.length} villa(s)'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      sl.logger.e('Error searching villas: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Search failed. Please try again.'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _navigateToVillaDetail(String villaId) async {
    final villaProvider = Provider.of<VillaProvider>(context, listen: false);

    // Use the new helper method to ensure villa data is available
    final dataAvailable = await villaProvider.ensureVillaDataById(villaId);

    if (!dataAvailable) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Villa details not available. Please try again.',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return;
    }

    // Get villa data and use smart navigation
    final villa = villaProvider.getVillaByIdExtended(villaId);
    if (villa != null) {
      AppRoutes.navigateToVillaOrGroup(context, villa);
    }
  }

  void _showVillaBottomSheet(ApiVilla villa) {
    if (mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildVillaBottomSheet(villa),
      );
    }
  }

  void _showFilterBottomSheet() {
    if (mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder:
            (context) => FilterBottomSheet(
              onFiltersApplied: () {
                // Refresh markers after filters are applied
                _addVillaMarkers();
              },
            ),
      );
    }
  }

  Widget _buildVillaBottomSheet(ApiVilla villa) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).bottomSheetTheme.backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16), // Standardized radius
          topRight: Radius.circular(16), // Standardized radius
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.078),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(
                vertical: 12.0,
              ), // Standardized margin
              width: 40, // Standardized size
              height: 4, // Standardized size
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          // Villa image
          Padding(
            padding: const EdgeInsets.fromLTRB(
              16,
              0,
              16,
              16,
            ), // Standardized padding
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12), // Standardized radius
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child:
                    villa.primaryImage.isNotEmpty
                        ? Image.network(
                          villa.primaryImage,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildBottomSheetPlaceholderImage();
                          },
                        )
                        : _buildBottomSheetPlaceholderImage(),
              ),
            ),
          ),

          // Villa details
          SingleChildScrollView(
            padding: const EdgeInsets.all(16.0), // Standardized padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Villa name and rating
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        villa.name,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          // color: theme.colorScheme.onSurface, // Default
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8, // Standardized padding
                        vertical: 4, // Standardized padding
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(
                          alpha: 0.15,
                        ), // Keep semantic amber
                        borderRadius: BorderRadius.circular(
                          8,
                        ), // Standardized radius
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: Colors.amber.shade700, // Keep semantic amber
                            size: 16, // Standardized size
                          ),
                          const SizedBox(width: 4),
                          Text(
                            villa.rating.toString(),
                            style: theme.textTheme.labelLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color:
                                  Colors.amber.shade800, // Keep semantic amber
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8), // Standardized spacing
                // Address
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined, // Using outlined version
                      color: theme.iconTheme.color?.withValues(alpha: 0.7),
                      size: 18, // Standardized size
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        villa.address.isNotEmpty ? villa.address : villa.area,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.textTheme.bodyMedium?.color?.withValues(
                            alpha: 0.7,
                          ),
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12), // Standardized spacing
                // Price
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 5,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(
                          alpha: 0.1,
                        ), // Keep semantic green
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '₹${villa.effectiveWeekdayPrice.toInt()}',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700, // Keep semantic green
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '/night',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.textTheme.bodyMedium?.color?.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16), // Standardized spacing
                // Villa features
                Wrap(
                  spacing: 8, // Standardized spacing
                  runSpacing: 8, // Standardized spacing
                  children: [
                    if (villa.noOfRoom > 0)
                      _buildFeatureChip(
                        Icons.bed_outlined,
                        '${villa.noOfRoom} Rooms',
                        theme,
                      ),
                    if (villa.noOfwashroom > 0)
                      _buildFeatureChip(
                        Icons.bathtub_outlined,
                        '${villa.noOfwashroom} Bathrooms',
                        theme,
                      ),
                    if (villa.noOfMemberTo > 0)
                      _buildFeatureChip(
                        Icons.people_alt_outlined,
                        'Up to ${villa.noOfMemberTo} guests',
                        theme,
                      ),
                    if (villa.isSwimmingPool)
                      _buildFeatureChip(Icons.pool_outlined, 'Pool', theme),
                    if (villa.wifi)
                      _buildFeatureChip(Icons.wifi_outlined, 'WiFi', theme),
                    if (villa.ac)
                      _buildFeatureChip(Icons.ac_unit_outlined, 'AC', theme),
                    if (villa.parking)
                      _buildFeatureChip(
                        Icons.local_parking_outlined,
                        'Parking',
                        theme,
                      ),
                  ],
                ),

                const SizedBox(height: 20), // Standardized spacing
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.colorScheme.primary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ), // Match villa_detail
                          ),
                          side: BorderSide(
                            color: theme.colorScheme.primary,
                            width: 1,
                          ),
                          textStyle: theme.textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        child: const Text('Close'),
                      ),
                    ),
                    const SizedBox(width: 12), // Standardized spacing
                    Expanded(
                      flex: 2,
                      child: ElevatedButton(
                        onPressed: () async {
                          Navigator.pop(context);
                          await _navigateToVillaDetail(villa.id.toString());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ), // Match villa_detail
                          ),
                          elevation: 2, // Subtle elevation
                          shadowColor: theme.colorScheme.primary.withValues(
                            alpha: 0.2,
                          ),
                          textStyle: theme.textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        child: const Text('View Details'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(IconData icon, String label, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ), // Standardized padding
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16), // Pill shape
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16, // Standardized size
            color: theme.colorScheme.onSurfaceVariant, // Muted color
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              // Standardized style
              color: theme.colorScheme.onSurfaceVariant, // Muted color
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSheetPlaceholderImage() {
    return Container(
      width: double.infinity,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.villa,
            size: 48,
            color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 8),
          Text(
            'No Image',
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Search bar
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: TextField(
                        controller: _searchController,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Search location or villa name',
                          hintStyle: TextStyle(
                            color: Theme.of(context).textTheme.bodyMedium?.color
                                ?.withValues(alpha: 0.6),
                          ),
                          border: InputBorder.none,
                          icon: Icon(
                            Icons.search,
                            color: Theme.of(
                              context,
                            ).iconTheme.color?.withValues(alpha: 0.7),
                          ),
                          suffixIcon:
                              _isSearchActive
                                  ? IconButton(
                                    icon: Icon(
                                      Icons.clear,
                                      color: Theme.of(
                                        context,
                                      ).iconTheme.color?.withValues(alpha: 0.7),
                                    ),
                                    onPressed: () {
                                      _searchController.clear();
                                      _performSearch('');
                                    },
                                    constraints: const BoxConstraints(
                                      minWidth: 20,
                                      minHeight: 20,
                                    ),
                                  )
                                  : null,
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 12,
                          ),
                          isDense: true,
                        ),
                        onSubmitted: (value) {
                          _performSearch(value);
                        },
                        textInputAction: TextInputAction.search,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.search,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                      onPressed: () {
                        _performSearch(_searchController.text);
                      },
                      constraints: const BoxConstraints(
                        minHeight: 40,
                        minWidth: 40,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      onPressed: () {
                        _showFilterBottomSheet();
                      },
                      constraints: const BoxConstraints(
                        minHeight: 40,
                        minWidth: 40,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Map view
          Expanded(
            child: Stack(
              children: [
                // Fallback UI in case of map error
                Container(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Center(
                    child:
                        _hasMapError
                            ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.map_outlined,
                                  size: 64,
                                  color: Theme.of(
                                    context,
                                  ).iconTheme.color?.withValues(alpha: 0.5),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Unable to load map',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        Theme.of(
                                          context,
                                        ).textTheme.headlineSmall?.color,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Please check your internet connection',
                                  style: TextStyle(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.color
                                        ?.withValues(alpha: 0.7),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            )
                            : const SizedBox.shrink(),
                  ),
                ),

                // Google Map with error handling
                if (!_hasMapError)
                  _isLoadingLocation
                      ? Center(
                        child: CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      )
                      : Builder(
                        builder: (context) {
                          try {
                            return GoogleMap(
                              initialCameraPosition: _initialCameraPosition,
                              markers: _markers,
                              myLocationEnabled: true, // Enable user location
                              myLocationButtonEnabled:
                                  true, // Enable location button
                              zoomControlsEnabled: true,
                              onMapCreated: (controller) async {
                                setState(() {
                                  _mapController = controller;
                                });

                                // If location was loaded before map creation, move camera
                                if (!_isLoadingLocation) {
                                  try {
                                    Position position =
                                        await LocationService.getCachedOrCurrentLocation();
                                    controller.animateCamera(
                                      CameraUpdate.newLatLng(
                                        LatLng(
                                          position.latitude,
                                          position.longitude,
                                        ),
                                      ),
                                    );
                                  } catch (e) {
                                    // Ignore error, map will use initial position
                                  }
                                }
                              },
                            );
                          } catch (e) {
                            // Handle error
                            Future.microtask(() {
                              setState(() {
                                _hasMapError = true;
                              });
                            });
                            return Container(); // Return empty container if map fails
                          }
                        },
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

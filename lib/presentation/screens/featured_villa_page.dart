import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/models/villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../data/providers/user_provider.dart';
import '../../routes.dart';
import '../../core/utils/location_service.dart';
import '../../core/utils/date_utils.dart' as app_date_utils;

class FeaturedVillaPage extends StatefulWidget {
  const FeaturedVillaPage({super.key});

  @override
  State<FeaturedVillaPage> createState() => _FeaturedVillaPageState();
}

class _FeaturedVillaPageState extends State<FeaturedVillaPage> {
  final ScrollController _scrollController = ScrollController();
  List<Villa> _allFeaturedVillas = [];
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  int _currentPage = 1;
  static const int _pageSize = 10;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMoreData) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _allFeaturedVillas.clear();
      _currentPage = 1;
      _hasMoreData = true;
    });
    await _loadMoreData();
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final villaProvider = Provider.of<VillaProvider>(context, listen: false);
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Get dynamic parameters
      final position = await LocationService.getCachedOrCurrentLocation();
      final dateRange = app_date_utils.DateUtils.getDashboardDateRange();
      final userId = userProvider.currentUser?.id ?? 1;
      final selectedCatId =
          villaProvider.selectedCategory?.id ??
          (villaProvider.categories.isNotEmpty
              ? villaProvider.categories.first.id
              : 1);
      final int? categoryIdToSend = selectedCatId == 0 ? null : selectedCatId;

      await villaProvider.fetchFeaturedDashboardData(
        lat: LocationService.latitudeToString(position.latitude),
        long: LocationService.longitudeToString(position.longitude),
        userId: userId,
        fromDate: dateRange['fromDate']!,
        toDate: dateRange['toDate']!,
        radius: 1500, // Default radius in meters as per requirements
        categoryId: categoryIdToSend,
        page: _currentPage,
        limit: _pageSize,
      );

      // Get the newly loaded villas
      final newVillas = villaProvider.featuredVillas;

      setState(() {
        if (newVillas.length < _pageSize) {
          _hasMoreData = false;
        }

        // Add new villas to the list, avoiding duplicates
        for (final villa in newVillas) {
          if (!_allFeaturedVillas.any((existing) => existing.id == villa.id)) {
            _allFeaturedVillas.add(villa);
          }
        }

        _currentPage++;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load more villas: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _refreshData() async {
    await _loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Featured Villas'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
      ),
      body: Consumer<VillaProvider>(
        builder: (context, villaProvider, child) {
          if (_allFeaturedVillas.isEmpty && villaProvider.isFeaturedLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_allFeaturedVillas.isEmpty &&
              villaProvider.featuredError != null) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.error,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Failed to load featured villas',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      villaProvider.featuredError ?? 'Unknown error occurred',
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _refreshData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (_allFeaturedVillas.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.villa_outlined,
                      color: Theme.of(
                        context,
                      ).iconTheme.color?.withValues(alpha: 0.5),
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No featured villas found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.headlineSmall?.color,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Try adjusting your search criteria or check back later.',
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _allFeaturedVillas.length + (_hasMoreData ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _allFeaturedVillas.length) {
                  // Loading indicator at the bottom
                  return _isLoadingMore
                      ? const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(child: CircularProgressIndicator()),
                      )
                      : const SizedBox.shrink();
                }

                final villa = _allFeaturedVillas[index];
                return _buildFeaturedVillaCard(villa);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildFeaturedVillaCard(Villa villa) {
    return GestureDetector(
      onTap: () {
        // Navigate to villa detail or villa group based on villaGroupId
        AppRoutes.navigateToVillaOrGroup(context, villa);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardTheme.color,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Villa image with bookmark icon
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  child: Image.network(
                    villa.imageUrl,
                    width: double.infinity,
                    height: 200,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: 200,
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                        child: Center(
                          child: Icon(
                            Icons.image_not_supported,
                            size: 48,
                            color: Theme.of(
                              context,
                            ).iconTheme.color?.withValues(alpha: 0.5),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(
                            context,
                          ).shadowColor.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Consumer<VillaProvider>(
                      builder: (context, provider, child) {
                        final isSaved = provider.isVillaSaved(villa.id);
                        return IconButton(
                          icon: Icon(
                            isSaved ? Icons.bookmark : Icons.bookmark_border,
                            color:
                                isSaved
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).iconTheme.color,
                          ),
                          onPressed: () async {
                            await provider.toggleSavedStatus(villa);
                          },
                          constraints: const BoxConstraints(
                            minHeight: 40,
                            minWidth: 40,
                          ),
                          padding: EdgeInsets.zero,
                        );
                      },
                    ),
                  ),
                ),
                // Featured badge
                Positioned(
                  top: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Featured',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            // Villa details
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Villa name
                  Text(
                    villa.name,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // Location
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Theme.of(
                          context,
                        ).iconTheme.color?.withValues(alpha: 0.7),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          villa.location,
                          style: TextStyle(
                            color: Theme.of(context).textTheme.bodyMedium?.color
                                ?.withValues(alpha: 0.7),
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Rating and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            villa.rating.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '₹${villa.price.toInt()}/night',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

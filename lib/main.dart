import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'core/utils/service_locator.dart';
import 'data/providers/booking_provider.dart';
import 'data/providers/theme_provider.dart';
import 'data/providers/user_provider.dart';
import 'data/providers/villa_provider.dart';
import 'data/providers/villa_group_provider.dart';
import 'routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize service locator
  await sl.init();

  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => VillaProvider()),
        ChangeNotifierProvider(create: (_) => VillaGroupProvider()),
        ChangeNotifierProvider(
          create: (_) => UserProvider(),
        ), // Moved UserProvider up
        ChangeNotifierProvider(
          create:
              (context) => BookingProvider(
                apiService: sl.apiService,
                userProvider: Provider.of<UserProvider>(context, listen: false),
              ),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'MastVilla',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.themeMode,
            initialRoute: AppRoutes.splash,
            routes: AppRoutes.getRoutes(),
          );
        },
      ),
    );
  }
}

import '../models/base_model.dart';

class ApiVilla extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final int adminId;
  final int villaGroupId;
  final String villaGroupName;
  final int status;
  final String name;
  final String desc;
  final String contactNumber;
  final String profilePicture;
  final List<String> images;
  final List<String> video;
  final double weekDayPrice;
  final double weekDayDiscountPrice;
  final double weekendPrice;
  final double weekendDiscountPrice;
  final int noOfMemberFrom;
  final int noOfMemberTo;
  final String lat;
  final String long;
  final String address;
  final bool isActive;
  final bool equipped;
  final String area;
  final int locationId;
  final bool kitchen;
  final bool complimentary;
  final bool meals;
  final bool parking;
  final bool ac;
  final int noOfAc;
  final bool chef;
  final int noOFcaretaker;
  final int noOfRoom;
  final int noOfBed;
  final int noOfMattress;
  final int noOfLivingRoom;
  final int noOfwashroom;
  final String direction;
  final String other;
  final bool isSwimmingPool;
  final String swimmingPoolMeasument;
  final bool isKidSwimmingPool;
  final bool wifi;
  final bool barbeque;
  final bool petFriendly;
  final bool fireExtinguisher;
  final int noOfFireExtinguisher;
  final double rating;
  final int ratingCount;
  final List<int> staffIds;
  final String nearby;
  final bool powerBackup;
  final List<int> whishlistUserIds;
  final bool planExpire;
  final String validFrom;
  final String validTill;
  final int categoryId;
  final List<int> amenitiesIds;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;
  final ApiStaff? staff;
  final ApiCategory? category;
  final ApiVillaGroup? villaGroup;
  final ApiLocation? location;
  final bool isFeatured;

  ApiVilla({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.adminId,
    required this.villaGroupId,
    required this.villaGroupName,
    required this.status,
    required this.name,
    required this.desc,
    required this.contactNumber,
    required this.profilePicture,
    required this.images,
    required this.video,
    required this.weekDayPrice,
    required this.weekDayDiscountPrice,
    required this.weekendPrice,
    required this.weekendDiscountPrice,
    required this.noOfMemberFrom,
    required this.noOfMemberTo,
    required this.lat,
    required this.long,
    required this.address,
    required this.isActive,
    required this.equipped,
    required this.area,
    required this.locationId,
    required this.kitchen,
    required this.complimentary,
    required this.meals,
    required this.parking,
    required this.ac,
    required this.noOfAc,
    required this.chef,
    required this.noOFcaretaker,
    required this.noOfRoom,
    required this.noOfBed,
    required this.noOfMattress,
    required this.noOfLivingRoom,
    required this.noOfwashroom,
    required this.direction,
    required this.other,
    required this.isSwimmingPool,
    required this.swimmingPoolMeasument,
    required this.isKidSwimmingPool,
    required this.wifi,
    required this.barbeque,
    required this.petFriendly,
    required this.fireExtinguisher,
    required this.noOfFireExtinguisher,
    required this.rating,
    required this.ratingCount,
    required this.staffIds,
    required this.nearby,
    required this.powerBackup,
    required this.whishlistUserIds,
    required this.planExpire,
    required this.validFrom,
    required this.validTill,
    required this.categoryId,
    required this.amenitiesIds,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
    this.staff,
    this.category,
    this.villaGroup,
    this.location,
    required this.isFeatured,
  });

  /// Creates an ApiVilla from JSON with better error handling
  factory ApiVilla.fromJsonSafe(Map<String, dynamic> json) {
    try {
      // Validate required fields
      if (json['id'] == null) {
        throw FormatException('Villa ID is required but was null');
      }
      
      return ApiVilla.fromJson(json);
    } catch (e) {
      throw FormatException(
        'Failed to parse ApiVilla safely. '
        'Villa ID: ${json['id']}, Name: ${json['name']}, '
        'Error: $e'
      );
    }
  }

  factory ApiVilla.fromJson(Map<String, dynamic> json) {
    try {
      return ApiVilla(
        id: _parseInt(json['id']) ?? 0,
        enabled: _parseBool(json['enabled']) ?? true,
        isDeleted: _parseBool(json['isDeleted']) ?? false,
        adminId: _parseInt(json['adminId']) ?? 0,
        villaGroupId: _parseInt(json['villaGroupId']) ?? 0,
        villaGroupName: _parseString(json['villaGroupName']) ?? '',
        status: _parseInt(json['status']) ?? 0,
        name: _parseString(json['name']) ?? '',
        desc: _parseString(json['desc']) ?? '',
        contactNumber: _parseString(json['contactNumber']) ?? '',
        profilePicture: _parseString(json['profilePicture']) ?? '',
        images: _parseStringList(json['images']) ?? [],
        video: _parseStringList(json['video']) ?? [],
        weekDayPrice: _parseDouble(json['weekDayPrice']) ?? 0.0,
        weekDayDiscountPrice: _parseDouble(json['weekDayDiscountPrice']) ?? 0.0,
        weekendPrice: _parseDouble(json['weekendPrice']) ?? 0.0,
        weekendDiscountPrice: _parseDouble(json['weekendDiscountPrice']) ?? 0.0,
        noOfMemberFrom: _parseInt(json['noOfMemberFrom']) ?? 0,
        noOfMemberTo: _parseInt(json['noOfMemberTo']) ?? 0,
        lat: _parseString(json['lat']) ?? '',
        long: _parseString(json['long']) ?? '',
        address: _parseString(json['address']) ?? '',
        isActive: _parseBool(json['isActive']) ?? true,
        equipped: _parseBool(json['equipped']) ?? true,
        area: _parseString(json['area']) ?? '',
        locationId: _parseInt(json['locationId']) ?? 0,
        kitchen: _parseBool(json['kitchen']) ?? true,
        complimentary: _parseBool(json['complimentary']) ?? true,
        meals: _parseBool(json['meals']) ?? true,
        parking: _parseBool(json['parking']) ?? true,
        ac: _parseBool(json['ac']) ?? true,
        noOfAc: _parseInt(json['noOfAc']) ?? 0,
        chef: _parseBool(json['chef']) ?? true,
        noOFcaretaker: _parseInt(json['noOFcaretaker']) ?? 0,
        noOfRoom: _parseInt(json['noOfRoom']) ?? 0,
        noOfBed: _parseInt(json['noOfBed']) ?? 0,
        noOfMattress: _parseInt(json['noOfMattress']) ?? 0,
        noOfLivingRoom: _parseInt(json['noOfLivingRoom']) ?? 0,
        noOfwashroom: _parseInt(json['noOfwashroom']) ?? 0,
        direction: _parseString(json['direction']) ?? '',
        other: _parseString(json['other']) ?? '',
        isSwimmingPool: _parseBool(json['isSwimmingPool']) ?? true,
        swimmingPoolMeasument: _parseString(json['swimmingPoolMeasument']) ?? '',
        isKidSwimmingPool: _parseBool(json['isKidSwimmingPool']) ?? true,
        wifi: _parseBool(json['wifi']) ?? true,
        barbeque: _parseBool(json['barbeque']) ?? true,
        petFriendly: _parseBool(json['petFriendly']) ?? true,
        fireExtinguisher: _parseBool(json['fireExtinguisher']) ?? true,
        noOfFireExtinguisher: _parseInt(json['noOfFireExtinguisher']) ?? 0,
        rating: _parseDouble(json['rating']) ?? 0.0,
        ratingCount: _parseInt(json['ratingCount']) ?? 0,
        staffIds: _parseIntList(json['staffIds']) ?? [],
        nearby: _parseString(json['nearby']) ?? '',
        powerBackup: _parseBool(json['powerBackup']) ?? true,
        whishlistUserIds: _parseIntList(json['whishlistUserIds']) ?? [],
        planExpire: _parseBool(json['planExpire']) ?? true,
        validFrom: _parseString(json['validFrom']) ?? '',
        validTill: _parseString(json['validTill']) ?? '',
        categoryId: _parseInt(json['categoryId']) ?? 0,
        amenitiesIds: _parseIntList(json['amenitiesIds']) ?? [],
        priority: _parseInt(json['priority']) ?? 0,
        createdBy: _parseInt(json['createdBy']) ?? 0,
        createdTimestamp: _parseString(json['createdTimestamp']) ?? '',
        updatedBy: _parseInt(json['updatedBy']) ?? 0,
        updatedTimestamp: _parseString(json['updatedTimestamp']) ?? '',
        staff: _parseNestedObject(json['staff'], (data) => ApiStaff.fromJson(data)),
        category: _parseNestedObject(json['category'], (data) => ApiCategory.fromJson(data)),
        villaGroup: _parseNestedObject(json['villaGroup'], (data) => ApiVillaGroup.fromJson(data)),
        location: _parseNestedObject(json['location'], (data) => ApiLocation.fromJson(data)),
        isFeatured: _parseBool(json['isFeatured']) ?? false,
      );
    } catch (e) {
      throw FormatException(
        'Failed to parse ApiVilla from JSON. '
        'Villa ID: ${json['id']}, Name: ${json['name']}, '
        'Error: $e, '
        'JSON: ${json.toString().length > 500 ? "${json.toString().substring(0, 500)}..." : json.toString()}',
      );
    }
  }

  // Helper parsing methods
  static String? _parseString(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static bool? _parseBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      final trimmedValue = value.trim().toLowerCase();
      return trimmedValue == 'true' || trimmedValue == '1' || trimmedValue == 'yes';
    }
    if (value is int) return value == 1;
    if (value is double) return value == 1.0;
    return false; // Default to false instead of null for better error handling
  }

  static List<String>? _parseStringList(dynamic value) {
    if (value == null) return null;
    if (value is List) {
      try {
        return value.map((item) => item.toString()).toList();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static List<int>? _parseIntList(dynamic value) {
    if (value == null) return null;
    if (value is List) {
      try {
        return value.map((item) => _parseInt(item) ?? 0).toList();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static T? _parseNestedObject<T>(dynamic value, T Function(Map<String, dynamic>) fromJson) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      try {
        return fromJson(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Debug method to safely inspect JSON data
  static String debugJsonData(Map<String, dynamic> json) {
    final buffer = StringBuffer();
    buffer.writeln('Villa JSON Debug Info:');
    buffer.writeln('ID: ${json['id']}');
    buffer.writeln('Name: ${json['name']}');
    buffer.writeln('Enabled: ${json['enabled']} (${json['enabled'].runtimeType})');
    buffer.writeln('PowerBackup: ${json['powerBackup']} (${json['powerBackup'].runtimeType})');
    
    // Check for any problematic boolean fields
    final boolFields = [
      'enabled', 'isDeleted', 'isActive', 'equipped', 'kitchen', 'complimentary',
      'meals', 'parking', 'ac', 'chef', 'isSwimmingPool', 'isKidSwimmingPool',
      'wifi', 'barbeque', 'petFriendly', 'fireExtinguisher', 'powerBackup', 'planExpire'
    ];
    
    for (final field in boolFields) {
      final value = json[field];
      if (value != null && value is String && value.length > 10) {
        buffer.writeln('POTENTIAL ISSUE - $field: "${value.substring(0, 10)}..." (truncated string)');
      }
    }
    
    return buffer.toString();
  }

  // Get latitude as double
  double? get latitude {
    try {
      return double.parse(lat);
    } catch (e) {
      return null;
    }
  }

  // Get longitude as double
  double? get longitude {
    try {
      return double.parse(long);
    } catch (e) {
      return null;
    }
  }

  // Get primary image
  String get primaryImage {
    if (profilePicture.isNotEmpty) return profilePicture;
    if (images.isNotEmpty) return images.first;
    return '';
  }

  // Get effective price (use discount if available)
  double get effectiveWeekdayPrice {
    return weekDayDiscountPrice > 0 ? weekDayDiscountPrice : weekDayPrice;
  }

  double get effectiveWeekendPrice {
    return weekendDiscountPrice > 0 ? weekendDiscountPrice : weekendPrice;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'adminId': adminId,
      'villaGroupId': villaGroupId,
      'villaGroupName': villaGroupName,
      'status': status,
      'name': name,
      'desc': desc,
      'contactNumber': contactNumber,
      'profilePicture': profilePicture,
      'images': images,
      'video': video,
      'weekDayPrice': weekDayPrice,
      'weekDayDiscountPrice': weekDayDiscountPrice,
      'weekendPrice': weekendPrice,
      'weekendDiscountPrice': weekendDiscountPrice,
      'noOfMemberFrom': noOfMemberFrom,
      'noOfMemberTo': noOfMemberTo,
      'lat': lat,
      'long': long,
      'address': address,
      'isActive': isActive,
      'equipped': equipped,
      'area': area,
      'locationId': locationId,
      'kitchen': kitchen,
      'complimentary': complimentary,
      'meals': meals,
      'parking': parking,
      'ac': ac,
      'noOfAc': noOfAc,
      'chef': chef,
      'noOFcaretaker': noOFcaretaker,
      'noOfRoom': noOfRoom,
      'noOfBed': noOfBed,
      'noOfMattress': noOfMattress,
      'noOfLivingRoom': noOfLivingRoom,
      'noOfwashroom': noOfwashroom,
      'direction': direction,
      'other': other,
      'isSwimmingPool': isSwimmingPool,
      'swimmingPoolMeasument': swimmingPoolMeasument,
      'isKidSwimmingPool': isKidSwimmingPool,
      'wifi': wifi,
      'barbeque': barbeque,
      'petFriendly': petFriendly,
      'fireExtinguisher': fireExtinguisher,
      'noOfFireExtinguisher': noOfFireExtinguisher,
      'rating': rating,
      'ratingCount': ratingCount,
      'staffIds': staffIds,
      'nearby': nearby,
      'powerBackup': powerBackup,
      'whishlistUserIds': whishlistUserIds,
      'planExpire': planExpire,
      'validFrom': validFrom,
      'validTill': validTill,
      'categoryId': categoryId,
      'amenitiesIds': amenitiesIds,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
      'staff': staff?.toJson(),
      'category': category?.toJson(),
      'villaGroup': villaGroup?.toJson(),
      'location': location?.toJson(),
      'isFeatured': isFeatured,
    };
}

  @override
  List<Object?> get props => [
    id,
    enabled,
    isDeleted,
    adminId,
    villaGroupId,
    villaGroupName,
    status,
    name,
    desc,
    contactNumber,
    profilePicture,
    images,
    video,
    weekDayPrice,
    weekDayDiscountPrice,
    weekendPrice,
    weekendDiscountPrice,
    noOfMemberFrom,
    noOfMemberTo,
    lat,
    long,
    address,
    isActive,
    equipped,
    area,
    locationId,
    kitchen,
    complimentary,
    meals,
    parking,
    ac,
    noOfAc,
    chef,
    noOFcaretaker,
    noOfRoom,
    noOfBed,
    noOfMattress,
    noOfLivingRoom,
    noOfwashroom,
    direction,
    other,
    isSwimmingPool,
    swimmingPoolMeasument,
    isKidSwimmingPool,
    wifi,
    barbeque,
    petFriendly,
    fireExtinguisher,
    noOfFireExtinguisher,
    rating,
    ratingCount,
    staffIds,
    nearby,
    powerBackup,
    whishlistUserIds,
    planExpire,
    validFrom,
    validTill,
    categoryId,
    amenitiesIds,
    priority,
    createdBy,
    createdTimestamp,
    updatedBy,
    updatedTimestamp,
    staff,
    category,
    villaGroup,
    location,
    isFeatured,
  ];
}

class ApiStaff extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final String firstName;
  final String lastName;
  final int gender;
  final String email;
  final String contactNumber;
  final String password;
  final int priority;
  final int adminsRoleId;
  final String dialCode;
  final String countryCode;
  final String profilePicture;
  final int villaId;
  final int parentAdminId;
  final int otpCount;
  final String otpTime;
  final int verifyCount;
  final String verifyTime;
  final bool otpBlock;
  final String googleAuthToken;
  final String appleAuthToken;
  final int createdBy;
  final String createdTimestamp;
  final String accessToken;
  final int updatedBy;
  final String updatedTimestamp;
  final ApiAdminsRole? adminsRole;

  ApiStaff({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.firstName,
    required this.lastName,
    required this.gender,
    required this.email,
    required this.contactNumber,
    required this.password,
    required this.priority,
    required this.adminsRoleId,
    required this.dialCode,
    required this.countryCode,
    required this.profilePicture,
    required this.villaId,
    required this.parentAdminId,
    required this.otpCount,
    required this.otpTime,
    required this.verifyCount,
    required this.verifyTime,
    required this.otpBlock,
    required this.googleAuthToken,
    required this.appleAuthToken,
    required this.createdBy,
    required this.createdTimestamp,
    required this.accessToken,
    required this.updatedBy,
    required this.updatedTimestamp,
    this.adminsRole,
  });

  factory ApiStaff.fromJson(Map<String, dynamic> json) {
    return ApiStaff(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      gender: json['gender'] ?? 1,
      email: json['email'] ?? '',
      contactNumber: json['contactNumber'] ?? '',
      password: json['password'] ?? '',
      priority: json['priority'] ?? 0,
      adminsRoleId: json['adminsRoleId'] ?? 0,
      dialCode: json['dialCode'] ?? '',
      countryCode: json['countryCode'] ?? '',
      profilePicture: json['profilePicture'] ?? '',
      villaId: json['villaId'] ?? 0,
      parentAdminId: json['parentAdminId'] ?? 0,
      otpCount: json['otpCount'] ?? 0,
      otpTime: json['otpTime'] ?? '',
      verifyCount: json['verifyCount'] ?? 0,
      verifyTime: json['verifyTime'] ?? '',
      otpBlock: json['otpBlock'] ?? true,
      googleAuthToken: json['googleAuthToken'] ?? '',
      appleAuthToken: json['appleAuthToken'] ?? '',
      createdBy: json['createdBy'] ?? 0,
      createdTimestamp: json['createdTimestamp'] ?? '',
      accessToken: json['accessToken'] ?? '',
      updatedBy: json['updatedBy'] ?? 0,
      updatedTimestamp: json['updatedTimestamp'] ?? '',
      adminsRole: json['adminsRole'] != null ? ApiAdminsRole.fromJson(json['adminsRole']) : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'firstName': firstName,
      'lastName': lastName,
      'gender': gender,
      'email': email,
      'contactNumber': contactNumber,
      'password': password,
      'priority': priority,
      'adminsRoleId': adminsRoleId,
      'dialCode': dialCode,
      'countryCode': countryCode,
      'profilePicture': profilePicture,
      'villaId': villaId,
      'parentAdminId': parentAdminId,
      'otpCount': otpCount,
      'otpTime': otpTime,
      'verifyCount': verifyCount,
      'verifyTime': verifyTime,
      'otpBlock': otpBlock,
      'googleAuthToken': googleAuthToken,
      'appleAuthToken': appleAuthToken,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'accessToken': accessToken,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
      'adminsRole': adminsRole?.toJson(),
    };
  }

  @override
  List<Object?> get props => [id, enabled, isDeleted, firstName, lastName, gender, email, contactNumber];
}

class ApiAdminsRole extends BaseModel {
  final int id;
  final bool enabled;
  final String name;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;

  ApiAdminsRole({
    required this.id,
    required this.enabled,
    required this.name,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
  });

  factory ApiAdminsRole.fromJson(Map<String, dynamic> json) {
    return ApiAdminsRole(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      name: json['name'] ?? '',
      priority: json['priority'] ?? 0,
      createdBy: json['createdBy'] ?? 0,
      createdTimestamp: json['createdTimestamp'] ?? '',
      updatedBy: json['updatedBy'] ?? 0,
      updatedTimestamp: json['updatedTimestamp'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'name': name,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
    };
  }

  @override
  List<Object?> get props => [id, enabled, name, priority, createdBy, createdTimestamp, updatedBy, updatedTimestamp];
}

class ApiCategory extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final String title;
  final String image;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;

  ApiCategory({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.title,
    required this.image,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
  });

  factory ApiCategory.fromJson(Map<String, dynamic> json) {
    return ApiCategory(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      title: json['title'] ?? '',
      image: json['image'] ?? '',
      priority: json['priority'] ?? 0,
      createdBy: json['createdBy'] ?? 0,
      createdTimestamp: json['createdTimestamp'] ?? '',
      updatedBy: json['updatedBy'] ?? 0,
      updatedTimestamp: json['updatedTimestamp'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'title': title,
      'image': image,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
    };
  }

  @override
  List<Object?> get props => [id, enabled, isDeleted, title, image, priority, createdBy, createdTimestamp, updatedBy, updatedTimestamp];
}

class ApiVillaGroup extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final int adminId;
  final String name;
  final String desc;
  final String contactNumber;
  final String profilePicture;
  final List<String> images;
  final List<String> video;
  final double startingPrice;
  final double endingPrice;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;
  final ApiStaff? admin;

  ApiVillaGroup({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.adminId,
    required this.name,
    required this.desc,
    required this.contactNumber,
    required this.profilePicture,
    required this.images,
    required this.video,
    required this.startingPrice,
    required this.endingPrice,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
    this.admin,
  });

  factory ApiVillaGroup.fromJson(Map<String, dynamic> json) {
    return ApiVillaGroup(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      adminId: json['adminId'] ?? 0,
      name: json['name'] ?? '',
      desc: json['desc'] ?? '',
      contactNumber: json['contactNumber'] ?? '',
      profilePicture: json['profilePicture'] ?? '',
      images: (json['images'] as List?)?.map((e) => e.toString()).toList() ?? [],
      video: (json['video'] as List?)?.map((e) => e.toString()).toList() ?? [],
      startingPrice: (json['startingPrice'] ?? 0).toDouble(),
      endingPrice: (json['endingPrice'] ?? 0).toDouble(),
      priority: json['priority'] ?? 0,
      createdBy: json['createdBy'] ?? 0,
      createdTimestamp: json['createdTimestamp'] ?? '',
      updatedBy: json['updatedBy'] ?? 0,
      updatedTimestamp: json['updatedTimestamp'] ?? '',
      admin: json['admin'] != null ? ApiStaff.fromJson(json['admin']) : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'adminId': adminId,
      'name': name,
      'desc': desc,
      'contactNumber': contactNumber,
      'profilePicture': profilePicture,
      'images': images,
      'video': video,
      'startingPrice': startingPrice,
      'endingPrice': endingPrice,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
      'admin': admin?.toJson(),
    };
  }

  @override
  List<Object?> get props => [id, enabled, isDeleted, adminId, name, desc, contactNumber, profilePicture, images, video, startingPrice, endingPrice, priority, createdBy, createdTimestamp, updatedBy, updatedTimestamp, admin];
}

class ApiLocation extends BaseModel {
  final int id;
  final bool enabled;
  final bool isDeleted;
  final String title;
  final String lat;
  final String long;
  final String image;
  final int priority;
  final int createdBy;
  final String createdTimestamp;
  final int updatedBy;
  final String updatedTimestamp;

  ApiLocation({
    required this.id,
    required this.enabled,
    required this.isDeleted,
    required this.title,
    required this.lat,
    required this.long,
    required this.image,
    required this.priority,
    required this.createdBy,
    required this.createdTimestamp,
    required this.updatedBy,
    required this.updatedTimestamp,
  });

  factory ApiLocation.fromJson(Map<String, dynamic> json) {
    return ApiLocation(
      id: json['id'] ?? 0,
      enabled: json['enabled'] ?? true,
      isDeleted: json['isDeleted'] ?? false,
      title: json['title'] ?? '',
      lat: json['lat'] ?? '',
      long: json['long'] ?? '',
      image: json['image'] ?? '',
      priority: json['priority'] ?? 0,
      createdBy: json['createdBy'] ?? 0,
      createdTimestamp: json['createdTimestamp'] ?? '',
      updatedBy: json['updatedBy'] ?? 0,
      updatedTimestamp: json['updatedTimestamp'] ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enabled': enabled,
      'isDeleted': isDeleted,
      'title': title,
      'lat': lat,
      'long': long,
      'image': image,
      'priority': priority,
      'createdBy': createdBy,
      'createdTimestamp': createdTimestamp,
      'updatedBy': updatedBy,
      'updatedTimestamp': updatedTimestamp,
    };
  }

  @override
  List<Object?> get props => [id, enabled, isDeleted, title, lat, long, image, priority, createdBy, createdTimestamp, updatedBy, updatedTimestamp];
}
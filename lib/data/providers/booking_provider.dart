import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Added for date formatting
import '../models/booking_history_model.dart';
import '../models/booking_model.dart';
import '../models/villa_model.dart';
import '../datasources/api_service.dart'; // Added for ApiService
import '../../core/constants/api_constants.dart'; // Import ApiConstants
import '../../core/utils/service_locator.dart'; // Will be removed if not used, or used for other services
import './user_provider.dart'; // Added for UserProvider

class BookingProvider extends ChangeNotifier {
  final ApiService _apiService; // To be injected via constructor
  final UserProvider _userProvider; // Added for UserProvider

  BookingModel? _currentBooking;
  List<BookingHistoryModel> _bookingHistory = [];
  bool _isFetchingBookingHistory = false;
  String? _bookingHistoryError;
  String _searchQuery = '';
  BookingStatus? _selectedStatus;

  // New state for availability
  List<DateTimeRange> _availableDates = [];
  List<DateTimeRange> _bookedDates = []; // To store booked dates
  bool _isFetchingAvailability = false;
  String? _availabilityError;

  // New state for booking confirmation
  bool _isConfirmingBooking = false;
  String? _bookingConfirmationError;

  BookingModel? get currentBooking => _currentBooking;
  List<BookingHistoryModel> get bookingHistory => _bookingHistory;
  String get searchQuery => _searchQuery;
  BookingStatus? get selectedStatus => _selectedStatus;

  List<DateTimeRange> get availableDates => _availableDates;
  List<DateTimeRange> get bookedDates => _bookedDates; // Getter for booked dates
  bool get isFetchingAvailability => _isFetchingAvailability;
  String? get availabilityError => _availabilityError;

  bool get isConfirmingBooking => _isConfirmingBooking;
  String? get bookingConfirmationError => _bookingConfirmationError;

  bool get isFetchingBookingHistory => _isFetchingBookingHistory;
  String? get bookingHistoryError => _bookingHistoryError;
  
  // Constructor to inject ApiService and UserProvider
  BookingProvider({required ApiService apiService, required UserProvider userProvider}) : _apiService = apiService, _userProvider = userProvider;

  // Initialize a new booking for a villa
  void initBooking(Villa villa) {
    _currentBooking = BookingModel(
      villaId: villa.id,
      pricePerNight: villa.price,
      numberOfNights: 2,
      cleaningFee: 50.0,
      serviceFee: 35.0,
    );
    notifyListeners();
  }

  // Update check-in date
  void updateCheckInDate(DateTime date) {
    if (_currentBooking == null) return;

    _currentBooking = _currentBooking!.copyWith(checkInDate: date);
    notifyListeners();
  }

  // Update check-out date
  void updateCheckOutDate(DateTime date) {
    if (_currentBooking == null) return;

    _currentBooking = _currentBooking!.copyWith(checkOutDate: date);

    // Calculate number of nights if both dates are set
    if (_currentBooking!.checkInDate != null &&
        _currentBooking!.checkOutDate != null) {
      final difference =
          _currentBooking!.checkOutDate!
              .difference(_currentBooking!.checkInDate!)
              .inDays;
      if (difference > 0) {
        _currentBooking = _currentBooking!.copyWith(numberOfNights: difference);
      }
    }

    notifyListeners();
  }

  // Set selected date range from calendar
  void setSelectedDateRange(DateTimeRange range) {
    if (_currentBooking == null) return;

    _currentBooking = _currentBooking!.copyWith(
      checkInDate: range.start,
      checkOutDate: range.end,
    );

    // Calculate number of nights
    final difference = range.end.difference(range.start).inDays;
    if (difference > 0) {
      _currentBooking = _currentBooking!.copyWith(numberOfNights: difference);
    } else {
      // Handle case where end date is not after start date, or same day
      _currentBooking = _currentBooking!.copyWith(numberOfNights: 1); // Or 0, depending on logic
    }
    notifyListeners();
  }

  // Update number of guests
  void updateNumberOfGuests(int guests) {
    if (_currentBooking == null) return;

    _currentBooking = _currentBooking!.copyWith(numberOfGuests: guests);
    notifyListeners();
  }

  // Fetch villa availability (now for a specific month/year)
  Future<void> fetchAvailability(String villaId, int month, int year) async {
    _isFetchingAvailability = true;
    _availabilityError = null;
    notifyListeners();

    try {
      final Map<String, dynamic> payload = {
        "villaId": int.tryParse(villaId) ?? 0, // Assuming villaId in payload is an int
        "month": month,
        "year": year,
      };

      // Make the API call to the new endpoint
      // Assuming _apiService.post exists and handles the base URL.
      // The endpoint "/booking/get-calendar" is used directly as per your request.
      final dynamic responseData = await _apiService.post("/booking/get-calendar", data: payload);
      
      _availableDates = []; // Reset before processing
      _bookedDates = []; // Reset booked dates as well

      if (responseData is Map<String, dynamic>) {
        if (responseData.containsKey('data') && responseData['data'] is List) {
          final List<dynamic> daysData = responseData['data'] as List<dynamic>;
          final List<DateTimeRange> newAvailableDates = [];
          final List<DateTimeRange> newBookedDates = []; // Temp list for booked dates
          int parseSuccessCount = 0;
          int parseFailCount = 0;

          if (daysData.isEmpty) {
            _availabilityError = null; // No dates returned for the month, not an error.
          } else {
            for (var dayEntry in daysData) {
              if (dayEntry is Map<String, dynamic>) {
                final String? dayString = dayEntry['day'] as String?;
                final bool? isBooked = dayEntry['booked'] as bool?;

                if (dayString != null && isBooked != null) {
                  final DateTime? parsedDate = DateTime.tryParse(dayString);
                  if (parsedDate != null) {
                    if (!isBooked) { // If not booked, it's available
                      newAvailableDates.add(DateTimeRange(start: parsedDate, end: parsedDate));
                      parseSuccessCount++;
                    } else { // If booked
                      newBookedDates.add(DateTimeRange(start: parsedDate, end: parsedDate));
                      // parseSuccessCount++; // Counting successful parsing of booked dates too if needed
                    }
                  } else {
                    parseFailCount++;
                    print('Warning: Could not parse date string from availability: $dayString');
                  }
                } else {
                  parseFailCount++;
                  print('Warning: Missing "day" or "booked" field, or incorrect type in availability entry: $dayEntry');
                }
              } else {
                parseFailCount++;
                print('Warning: Unexpected item type in "data" list: ${dayEntry.runtimeType}');
              }
            }
            _availableDates = newAvailableDates;
            _bookedDates = newBookedDates; // Assign parsed booked dates

            // Error handling can remain similar, or be adjusted if needed for booked dates
            if (parseFailCount > 0 && parseSuccessCount == 0 && newBookedDates.isEmpty && daysData.isNotEmpty) {
              _availabilityError = "Received availability data, but failed to parse any valid date entries.";
            } else if (parseFailCount > 0) {
              _availabilityError = null;
            } else {
              _availabilityError = null;
            }
          }
        } else {
          _availabilityError = "Unexpected availability data format: 'data' key missing or not a List.";
        }
      } else if (responseData == null) {
        _availabilityError = "No availability data received (API returned null).";
      } else {
        _availabilityError = "Unexpected availability data format: Expected a Map, got ${responseData.runtimeType}.";
      }

    } catch (e) {
      _availabilityError = "Failed to fetch availability: ${e.toString()}";
      _availableDates = [];
      _bookedDates = []; // Clear booked dates on error too
      // Consider logging the full error with stack trace for internal debugging
      // e.g., using a proper logger: sl.logger.e('Fetch Availability Error', error: e, stackTrace: s);
      print('Fetch Availability Exception: $e');
    } finally {
      _isFetchingAvailability = false;
      notifyListeners();
    }
  }

  // Complete booking - calls the new booking API
  Future<bool> confirmBooking() async {
    if (_currentBooking == null ||
        _currentBooking!.checkInDate == null ||
        _currentBooking!.checkOutDate == null ||
        _currentBooking!.villaId.isEmpty) {
      _bookingConfirmationError = "Booking details are incomplete.";
      notifyListeners();
      return false;
    }

    _isConfirmingBooking = true;
    _bookingConfirmationError = null;
    notifyListeners();

    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    final int currentUserId = _userProvider.currentUser?.id ?? 0;

    final Map<String, dynamic> bookingData = {
      "villaId": int.tryParse(_currentBooking!.villaId) ?? 0,
      "fromDate": formatter.format(_currentBooking!.checkInDate!),
      "toDate": formatter.format(_currentBooking!.checkOutDate!),
      "userId": currentUserId,
      // "adminId": 0, // Removed
      "status": 0, // Initial status, e.g., "Pending"
      "amount": _currentBooking!.totalPrice,
      "noOfDay": _currentBooking!.numberOfNights,
      "noOfAdult": _currentBooking!.numberOfGuests,
      "noOfChildren": _currentBooking!.numberOfChildren ?? 0,
      // "paymentMode": _currentBooking!.paymentMode ?? "Online", // Removed
      // "paymentStatus": _currentBooking!.paymentStatus ?? "Pending", // Removed
      // "bookingDate": formatter.format(DateTime.now()), // Removed
      "remark": _currentBooking!.remark ?? "",

      // "user": { // Removed
      //   "userId": currentUserId,
      // },
      // "villa": { // Removed
      //   "id": int.tryParse(_currentBooking!.villaId) ?? 0,
      // }
    };

    try {
      final Map<String, dynamic>? response = await _apiService.post(ApiConstants.newBooking, data: bookingData);

      if (response != null) {
        if (response['success'] == true && response.containsKey('data')) {
          // Assuming 'data' contains the created booking, and 'id' might be inside 'data'
          final bookingDetails = response['data'] as Map<String, dynamic>?;
          final bookingId = bookingDetails?['id']; // Or response['bookingId'] directly if flat

          print('Booking successful. ID: ${bookingId ?? 'N/A'}. Message: ${response['message']}');
          // Optionally, you could store the new bookingId or full booking details if needed.
          // _currentBooking = BookingModel.fromJson(bookingDetails); // If API returns full model
          return true;
        } else {
          // API indicated failure or unexpected success format
          _bookingConfirmationError = response['message'] as String? ?? "Booking failed. Please try again.";
          print('Booking failed. API Response: $response');
          return false;
        }
      } else {
        // Null response from ApiService (should ideally be handled by ApiService throwing an exception)
        _bookingConfirmationError = "Booking failed: No response from server.";
        print('Booking failed: Null response from ApiService.');
        return false;
      }
    } catch (e) {
      // This will catch DioExceptions or other exceptions rethrown by ApiService
      print('Error during booking confirmation API call: $e');
      // TODO: Parse 'e' if it's a known ApiException type to get a more specific message
      _bookingConfirmationError = "An error occurred while confirming booking: ${e.toString()}";
      return false;
    } finally {
      _isConfirmingBooking = false;
      notifyListeners();
    }
  }

  // Update the current booking with a new BookingModel
  void updateCurrentBooking(BookingModel booking) {
    _currentBooking = booking;
    notifyListeners();
  }

  // Clear current booking
  void clearBooking() {
    _currentBooking = null;
    notifyListeners();
  }

  // Fetch actual booking history from API, now accepts an optional status for filtering
  Future<void> fetchUserBookingHistory({BookingStatus? status}) async {
    // If a status is provided by the filter, update _selectedStatus
    // This ensures _selectedStatus is in sync if fetch is called directly with a status
    if (status != null) {
      _selectedStatus = status;
    }

    final currentUserId = _userProvider.currentUser?.id;
    if (currentUserId == null) {
      _bookingHistoryError = "User not logged in.";
      _bookingHistory = [];
      notifyListeners();
      return;
    }

    _isFetchingBookingHistory = true;
    _bookingHistoryError = null;
    notifyListeners();

    try {
      // Construct the endpoint with the filter
      String endpoint = '${ApiConstants.newBooking}?filter=userId||eq||$currentUserId';
      
      // Add status filter if _selectedStatus is not null
      if (_selectedStatus != null) {
        int statusCode;
        switch (_selectedStatus!) {
          case BookingStatus.pending:
            statusCode = 0;
            break;
          case BookingStatus.approved:
            statusCode = 1;
            break;
          case BookingStatus.cancelled:
            statusCode = 2;
            break;
        }
        endpoint += '&status||eq||$statusCode';
      }
      
      final dynamic responseData = await _apiService.get(endpoint); // Assuming a 'get' method in ApiService

      if (responseData != null && responseData is Map<String, dynamic>) {
        if (responseData.containsKey('data') && responseData['data'] is List) {
          final List<dynamic> historyData = responseData['data'] as List<dynamic>;
          if (historyData.isNotEmpty) {
            _bookingHistory = historyData
                .map((item) => BookingHistoryModel.fromJson(item as Map<String, dynamic>))
                .toList();
            _bookingHistoryError = null;
          } else {
            _bookingHistory = []; // No bookings found
            _bookingHistoryError = null; // Not an error, just no data
          }
        } else {
           _bookingHistory = [];
          _bookingHistoryError = "Unexpected data format in API response: 'data' key missing or not a List.";
        }
      } else if (responseData != null && responseData is List) {
        // Handle cases where the API directly returns a list
         if (responseData.isNotEmpty) {
            _bookingHistory = responseData
                .map((item) => BookingHistoryModel.fromJson(item as Map<String, dynamic>))
                .toList();
            _bookingHistoryError = null;
          } else {
            _bookingHistory = []; // No bookings found
            _bookingHistoryError = null; // Not an error, just no data
          }
      }
      else {
        _bookingHistory = [];
        _bookingHistoryError = "Failed to fetch booking history: No data received or unexpected format.";
      }
    } catch (e) {
      _bookingHistory = [];
      _bookingHistoryError = "Failed to fetch booking history: ${e.toString()}";
      print('Fetch Booking History Exception: $e');
    } finally {
      _isFetchingBookingHistory = false;
      notifyListeners();
    }
  }

  // Initialize booking history with mock data (can be removed or updated to call fetchUserBookingHistory)
  void initBookingHistory() {
    // Consider calling fetchUserBookingHistory here or removing mock data initialization
    // For now, keeping mock data as a fallback or for testing if API is not ready.
    // To use API: fetchUserBookingHistory(); return;

    // To use API: fetchUserBookingHistory(status: _selectedStatus); return; // Pass current filter

    _bookingHistory = [
      BookingHistoryModel(
        id: 'BK-2024-001',
        type: BookingType.hotel,
        status: BookingStatus.pending, // Updated mock data
        startDate: DateTime(2024, 2, 15),
        endDate: DateTime(2024, 2, 20),
        location: 'Grand Hotel, New York',
        imageUrl:
            'https://images.unsplash.com/photo-1566073771259-6a8506099945?q=80&w=1000',
        price: 599,
      ),
      BookingHistoryModel(
        id: 'BK-2024-002',
        type: BookingType.flight,
        status: BookingStatus.approved, // Updated mock data
        startDate: DateTime(2024, 1, 20),
        endDate: DateTime(2024, 1, 25),
        location: 'London - Paris',
        imageUrl:
            'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?q=80&w=1000',
        price: 349,
      ),
      BookingHistoryModel(
        id: 'BK-2024-003',
        type: BookingType.hotel,
        status: BookingStatus.cancelled,
        startDate: DateTime(2023, 12, 10),
        endDate: DateTime(2023, 12, 15),
        location: 'Beach Resort, Maldives',
        imageUrl:
            'https://images.unsplash.com/photo-1602002418082-dd4a8f7d317c?q=80&w=1000',
        price: 899,
      ),
    ];
    notifyListeners();
  }

  // Filter booking history by status
  List<BookingHistoryModel> getFilteredBookingHistory() {
    if (_selectedStatus == null && _searchQuery.isEmpty) {
      return _bookingHistory;
    }

    return _bookingHistory.where((booking) {
      // Filter by status if selected
      final statusMatch =
          _selectedStatus == null || booking.status == _selectedStatus;

      // Filter by search query if not empty
      final searchMatch =
          _searchQuery.isEmpty ||
          booking.location.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          booking.id.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          booking.typeDisplayName.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );

      return statusMatch && searchMatch;
    }).toList();
  }

  // Set selected status filter and fetch new data
  void setStatusFilter(BookingStatus? status) {
    _selectedStatus = status;
    // Fetch new history based on the selected status
    fetchUserBookingHistory(status: _selectedStatus);
    // notifyListeners(); // fetchUserBookingHistory will call notifyListeners
  }

  // Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
}

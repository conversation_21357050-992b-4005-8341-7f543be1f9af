import 'package:dio/dio.dart';
import '../../core/constants/api_constants.dart';
import '../../core/network/dio_client.dart';

/// Generic API service for making network requests
class ApiService {
  final DioClient _dioClient;

  ApiService({required DioClient dioClient}) : _dioClient = dioClient;

  /// Set authentication token
  void setAuthToken(String token) {
    _dioClient.setAuthToken(token);
  }

  /// Clear authentication token
  void clearAuthToken() {
    _dioClient.clearAuthToken();
  }

  /// Perform a GET request
  Future<dynamic> get(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dioClient.get(
        endpoint,
        queryParameters: queryParams,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Perform a POST request
  Future<dynamic> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParams,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dioClient.post(
        endpoint,
        data: data,
        queryParameters: queryParams,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Perform a PUT request
  Future<dynamic> put(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParams,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dioClient.put(
        endpoint,
        data: data,
        queryParameters: queryParams,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Perform a PATCH request
  Future<dynamic> patch(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParams,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dioClient.patch(
        endpoint,
        data: data,
        queryParameters: queryParams,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Perform a DELETE request
  Future<dynamic> delete(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParams,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dioClient.delete(
        endpoint,
        data: data,
        queryParameters: queryParams,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Download a file
  Future<void> downloadFile(
    String url,
    String savePath, {
    ProgressCallback? onProgress,
    Map<String, dynamic>? queryParams,
    CancelToken? cancelToken,
  }) async {
    try {
      await _dioClient.download(
        url,
        savePath,
        onReceiveProgress: onProgress,
        queryParameters: queryParams,
        cancelToken: cancelToken,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Convenience methods for common API operations

  /// Login user
  Future<dynamic> login(String email, String password) async {
    return await post(
      ApiConstants.login,
      data: {'email': email, 'password': password},
    );
  }

  /// Register user
  Future<dynamic> register(Map<String, dynamic> userData) async {
    return await post(ApiConstants.register, data: userData);
  }

  /// Get user profile
  Future<dynamic> getUserProfile() async {
    return await get(ApiConstants.userProfile);
  }

  /// Update user profile
  Future<dynamic> updateUserProfile(Map<String, dynamic> userData) async {
    return await put(ApiConstants.updateProfile, data: userData);
  }

  /// Get villas with optional filters
  Future<dynamic> getVillas({Map<String, dynamic>? filters}) async {
    return await get(ApiConstants.villas, queryParams: filters);
  }

  /// Get all villas from /villa endpoint
  Future<dynamic> getAllVillas() async {
    return await get(ApiConstants.villa);
  }

  /// Get villa details by ID
  Future<dynamic> getVillaDetails(String villaId) async {
    return await get('${ApiConstants.villaDetails}/$villaId');
  }

  /// Search villas
  Future<dynamic> searchVillas(
    String query, {
    Map<String, dynamic>? filters,
  }) async {
    final params = {'q': query, ...?filters};
    return await get(ApiConstants.searchVillas, queryParams: params);
  }

  /// Get featured villas
  Future<dynamic> getFeaturedVillas() async {
    return await get(ApiConstants.featuredVillas);
  }

  /// Get villa group list
  Future<dynamic> getVillaGroupList({
    required String fromDate,
    required String toDate,
    required int villaGroupId,
  }) async {
    return await post(
      ApiConstants.villaGroupList,
      data: {
        'fromDate': fromDate,
        'toDate': toDate,
        'villaGroupId': villaGroupId,
      },
    );
  }

  /// Create a booking
  Future<dynamic> createBooking(Map<String, dynamic> bookingData) async {
    return await post(ApiConstants.createBooking, data: bookingData);
  }

  /// Get booking history
  Future<dynamic> getBookingHistory() async {
    return await get(ApiConstants.bookingHistory);
  }

  /// Cancel a booking
  Future<dynamic> cancelBooking(String bookingId) async {
    return await post('${ApiConstants.cancelBooking}/$bookingId');
  }

  /// Check villa availability
  Future<dynamic> checkAvailability(
    String villaId,
    String fromDate,
    String toDate,
  ) async {
    // Ensure the body matches the API expectation: {villaId:12, to:date, from:date}
    return await post(
      ApiConstants.checkAvailability,
      data: {
        'villaId': int.tryParse(villaId) ?? 0, // Assuming villaId in API is int
        'from': fromDate,
        'to': toDate,
      },
    );
  }

  /// Get chats
  Future<dynamic> getChats() async {
    return await get(ApiConstants.chats);
  }

  /// Get messages for a chat
  Future<dynamic> getMessages(String chatId) async {
    return await get('${ApiConstants.messages}/$chatId');
  }

  /// Send a message
  Future<dynamic> sendMessage(String chatId, String message) async {
    return await post(
      ApiConstants.sendMessage,
      data: {'chatId': chatId, 'message': message},
    );
  }

  /// Refresh authentication token
  Future<dynamic> refreshToken(String refreshToken) async {
    return await post(
      ApiConstants.refreshToken,
      data: {'refreshToken': refreshToken},
    );
  }

  /// Logout user
  Future<dynamic> logout() async {
    try {
      await post(ApiConstants.logout);
    } finally {
      clearAuthToken();
    }
  }

  /// Verify OTP
  Future<dynamic> verifyOtp(String contactNumber, String otp) async {
    return await post(
      ApiConstants.verifyOtp,
      data: {'contactNumber': contactNumber, 'otp': otp},
    );
  }

  /// Add new user (registration)
  Future<dynamic> addUser(Map<String, dynamic> userData) async {
    return await post(ApiConstants.addUser, data: userData);
  }

  /// Get dashboard data with location and filter parameters
  Future<dynamic> getDashboardData({
    required String lat,
    required String long,
    required int userId,
    required String fromDate,
    required String toDate,
    required int radius,
    int? categoryId, // Made nullable
    int? page, // Optional pagination
    int? limit, // Optional pagination
  }) async {
    final Map<String, dynamic> requestData = {
      'lat': lat,
      'long': long,
      'userId': userId,
      'fromDate': fromDate,
      'toDate': toDate,
      'radius': radius,
    };
    if (categoryId != null) {
      requestData['categoryId'] = categoryId;
    }
    if (page != null) {
      requestData['page'] = page;
    }
    if (limit != null) {
      requestData['limit'] = limit;
    }
    return await post(ApiConstants.usersDashboard, data: requestData);
  }

  /// Get featured dashboard data with location and filter parameters
  Future<dynamic> getFeaturedDashboardData({
    required String lat,
    required String long,
    required int userId,
    required String fromDate,
    required String toDate,
    required int radius,
    int? categoryId, // Made nullable
    int? page, // Optional pagination
    int? limit, // Optional pagination
  }) async {
    final Map<String, dynamic> requestData = {
      'lat': lat,
      'long': long,
      'userId': userId,
      'fromDate': fromDate,
      'toDate': toDate,
      'radius': radius,
    };
    if (categoryId != null) {
      requestData['categoryId'] = categoryId;
    }
    if (page != null) {
      requestData['page'] = page;
    }
    if (limit != null) {
      requestData['limit'] = limit;
    }
    return await post(ApiConstants.usersFeaturedDashboard, data: requestData);
  }

  /// Get all categories
  Future<dynamic> getCategories() async {
    return await get(ApiConstants.categories);
  }

  /// Get all amenities master
  Future<dynamic> getAmenitiesMaster() async {
    return await get(ApiConstants.amenitiesMaster);
  }

  /// Get user's wishlist with filter
  Future<dynamic> getWishlist({required int userId}) async {
    return await get(
      ApiConstants.wishlist,
      queryParams: {'filter': 'userId||eq||$userId'},
    );
  }

  /// Add villa to wishlist
  Future<dynamic> addToWishlist(Map<String, dynamic> wishlistData) async {
    return await post(ApiConstants.addToWishlist, data: wishlistData);
  }

  /// Remove villa from wishlist (by calling the same add endpoint)
  Future<dynamic> removeFromWishlist(Map<String, dynamic> wishlistData) async {
    // To remove, call the same endpoint as adding, with the same body.
    // The backend will handle it as a toggle.
    return await post(ApiConstants.addToWishlist, data: wishlistData);
  }

  /// Advance search for villas with filters
  Future<dynamic> advanceSearch(Map<String, dynamic> searchData) async {
    return await post(ApiConstants.advanceSearch, data: searchData);
  }
}

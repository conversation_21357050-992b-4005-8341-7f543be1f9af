class ApiConstants {
  ApiConstants._();

  // Base URL
  static const String baseUrl = 'https://api.mastvilla.com';

  // Authentication
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String refreshToken = '/auth/refresh';
  static const String logout = '/auth/logout';

  // User endpoints
  static const String userProfile = '/user/profile';
  static const String updateProfile = '/user/update';
  static const String verifyOtp = '/users/verify-otp';
  static const String addUser = '/users/add';
  static const String usersDashboard = '/users/dashboard';
  static const String usersFeaturedDashboard = '/users/featured-dashboard';

  // Category endpoints
  static const String categories = '/category';

  // Amenities endpoints
  static const String amenitiesMaster = '/amenities-master';

  // Villa endpoints
  static const String villas = '/villas';
  static const String villa = '/villa';
  static const String villaDetails = '/villa';
  static const String searchVillas = '/villas/search';
  static const String featuredVillas = '/villas/featured';
  static const String advanceSearch = '/users/advance-search';
  static const String villaGroupList = '/users/villagroup-list';

  // Wishlist endpoints
  static const String wishlist = '/whislist';
  static const String addToWishlist = '/whislist/add';

  // Booking endpoints
  static const String bookings = '/bookings';
  static const String bookingHistory = '/bookings/history';
  static const String createBooking = '/bookings/create';
  static const String cancelBooking = '/bookings/cancel';
  static const String newBooking =
      '/booking'; // New endpoint for creating a booking
  static const String checkAvailability =
      '/booking/check-availability'; // New endpoint for checking availability

  // Chat endpoints
  static const String chats = '/chats';
  static const String messages = '/messages';
  static const String sendMessage = '/messages/send';

  // Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  // Headers
  static const String contentType = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearerPrefix = 'Bearer ';
}
